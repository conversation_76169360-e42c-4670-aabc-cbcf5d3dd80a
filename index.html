<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>哔哩哔哩 (゜-゜)つロ 干杯~-bilibili</title>
    <link rel="icon"
        href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'><text y='20' font-size='20'>📺</text></svg>">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background-color: #f4f5f7;
            color: #333;
            line-height: 1.6;
        }

        /* 头部导航 */
        .header {
            background: #fff;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            position: sticky;
            top: 0;
            z-index: 1000;
        }

        .nav-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
            display: flex;
            align-items: center;
            height: 64px;
        }

        .logo {
            font-size: 24px;
            font-weight: bold;
            color: #00a1d6;
            text-decoration: none;
            margin-right: 40px;
        }

        .search-box {
            flex: 1;
            max-width: 400px;
            position: relative;
        }

        .search-input {
            width: 100%;
            height: 40px;
            border: 2px solid #e7e7e7;
            border-radius: 20px;
            padding: 0 50px 0 20px;
            font-size: 14px;
            outline: none;
            transition: border-color 0.3s;
        }

        .search-input:focus {
            border-color: #00a1d6;
        }

        .search-btn {
            position: absolute;
            right: 5px;
            top: 50%;
            transform: translateY(-50%);
            background: #00a1d6;
            border: none;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .nav-menu {
            display: flex;
            list-style: none;
            margin-left: 40px;
        }

        .nav-menu li {
            margin: 0 20px;
        }

        .nav-menu a {
            text-decoration: none;
            color: #333;
            font-size: 14px;
            transition: color 0.3s;
        }

        .nav-menu a:hover {
            color: #00a1d6;
        }

        /* 主要内容区域 */
        .main-container {
            max-width: 1200px;
            margin: 20px auto;
            padding: 0 20px;
        }

        /* 分类导航 */
        .category-nav {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .category-list {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
        }

        .category-item {
            padding: 8px 16px;
            background: #f1f2f3;
            border-radius: 20px;
            text-decoration: none;
            color: #333;
            font-size: 14px;
            transition: all 0.3s;
        }

        .category-item:hover,
        .category-item.active {
            background: #00a1d6;
            color: white;
        }

        /* 视频网格 */
        .video-grid {
            display: grid;
            gap: 20px;
            grid-template-columns: repeat(5, 1fr);
            /* PC端5列 */
        }

        .video-card {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s, box-shadow 0.3s;
            cursor: pointer;
        }

        .video-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
        }

        .video-thumbnail {
            position: relative;
            width: 100%;
            height: 140px;
            background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 48px;
        }

        .video-duration {
            position: absolute;
            bottom: 8px;
            right: 8px;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 12px;
        }

        .video-info {
            padding: 12px;
        }

        .video-title {
            font-size: 14px;
            font-weight: 500;
            line-height: 1.4;
            margin-bottom: 8px;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .video-meta {
            display: flex;
            align-items: center;
            justify-content: space-between;
            font-size: 12px;
            color: #999;
        }

        .video-author {
            color: #666;
        }

        .video-stats {
            display: flex;
            gap: 10px;
        }

        /* 响应式设计 */
        @media (max-width: 1024px) {
            .video-grid {
                grid-template-columns: repeat(3, 1fr);
                /* iPad 3列 */
            }

            .nav-menu {
                display: none;
            }

            .search-box {
                max-width: 300px;
            }
        }

        @media (max-width: 768px) {
            .video-grid {
                grid-template-columns: repeat(2, 1fr);
                /* 移动设备2列 */
                gap: 15px;
            }

            .main-container {
                padding: 0 15px;
            }

            .nav-container {
                padding: 0 15px;
            }

            .logo {
                margin-right: 20px;
                font-size: 20px;
            }

            .search-box {
                max-width: 200px;
            }

            .category-nav {
                padding: 15px;
            }

            .category-list {
                gap: 10px;
            }

            .video-thumbnail {
                height: 120px;
                font-size: 36px;
            }
        }

        @media (max-width: 480px) {
            .video-thumbnail {
                height: 100px;
                font-size: 24px;
            }

            .video-info {
                padding: 10px;
            }

            .video-title {
                font-size: 13px;
            }
        }
    </style>
</head>

<body>
    <!-- 头部导航 -->
    <header class="header">
        <div class="nav-container">
            <a href="#" class="logo">哔哩哔哩</a>
            <div class="search-box">
                <input type="text" class="search-input" placeholder="搜索你感兴趣的内容">
                <button class="search-btn">🔍</button>
            </div>
            <ul class="nav-menu">
                <li><a href="#">首页</a></li>
                <li><a href="#">动画</a></li>
                <li><a href="#">番剧</a></li>
                <li><a href="#">国创</a></li>
                <li><a href="#">音乐</a></li>
                <li><a href="#">舞蹈</a></li>
                <li><a href="#">游戏</a></li>
                <li><a href="#">科技</a></li>
            </ul>
        </div>
    </header>

    <!-- 主要内容 -->
    <main class="main-container">
        <!-- 分类导航 -->
        <nav class="category-nav">
            <div class="category-list">
                <a href="#" class="category-item active">推荐</a>
                <a href="#" class="category-item">热门</a>
                <a href="#" class="category-item">动画</a>
                <a href="#" class="category-item">音乐</a>
                <a href="#" class="category-item">舞蹈</a>
                <a href="#" class="category-item">游戏</a>
                <a href="#" class="category-item">知识</a>
                <a href="#" class="category-item">科技</a>
                <a href="#" class="category-item">运动</a>
                <a href="#" class="category-item">汽车</a>
                <a href="#" class="category-item">生活</a>
                <a href="#" class="category-item">美食</a>
            </div>
        </nav>

        <!-- 视频网格 -->
        <div class="video-grid">
            <!-- 视频卡片 1 -->
            <div class="video-card">
                <div class="video-thumbnail">
                    🎮
                    <div class="video-duration">12:34</div>
                </div>
                <div class="video-info">
                    <div class="video-title">【游戏解说】最新热门游戏深度体验，画面震撼到爆炸！</div>
                    <div class="video-meta">
                        <div class="video-author">游戏大神UP</div>
                        <div class="video-stats">
                            <span>👁 12.5万</span>
                            <span>👍 8.2k</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 视频卡片 2 -->
            <div class="video-card">
                <div class="video-thumbnail">
                    🎵
                    <div class="video-duration">04:23</div>
                </div>
                <div class="video-info">
                    <div class="video-title">【翻唱】超好听的古风歌曲，听一遍就会爱上</div>
                    <div class="video-meta">
                        <div class="video-author">音乐小仙女</div>
                        <div class="video-stats">
                            <span>👁 45.2万</span>
                            <span>👍 15.6k</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 视频卡片 3 -->
            <div class="video-card">
                <div class="video-thumbnail">
                    💃
                    <div class="video-duration">03:45</div>
                </div>
                <div class="video-info">
                    <div class="video-title">【舞蹈】超燃街舞表演，这个动作太帅了！</div>
                    <div class="video-meta">
                        <div class="video-author">舞蹈达人</div>
                        <div class="video-stats">
                            <span>👁 28.7万</span>
                            <span>👍 12.3k</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 视频卡片 4 -->
            <div class="video-card">
                <div class="video-thumbnail">
                    🔬
                    <div class="video-duration">15:20</div>
                </div>
                <div class="video-info">
                    <div class="video-title">【科普】宇宙的奥秘：黑洞到底是什么？</div>
                    <div class="video-meta">
                        <div class="video-author">科学探索者</div>
                        <div class="video-stats">
                            <span>👁 67.8万</span>
                            <span>👍 25.4k</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 视频卡片 5 -->
            <div class="video-card">
                <div class="video-thumbnail">
                    🍳
                    <div class="video-duration">08:15</div>
                </div>
                <div class="video-info">
                    <div class="video-title">【美食】5分钟学会做超好吃的蛋炒饭</div>
                    <div class="video-meta">
                        <div class="video-author">美食小当家</div>
                        <div class="video-stats">
                            <span>👁 89.3万</span>
                            <span>👍 32.1k</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 视频卡片 6 -->
            <div class="video-card">
                <div class="video-thumbnail">
                    📱
                    <div class="video-duration">10:42</div>
                </div>
                <div class="video-info">
                    <div class="video-title">【数码】最新手机评测，性价比之王诞生！</div>
                    <div class="video-meta">
                        <div class="video-author">数码评测师</div>
                        <div class="video-stats">
                            <span>👁 156.7万</span>
                            <span>👍 45.8k</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 视频卡片 7 -->
            <div class="video-card">
                <div class="video-thumbnail">
                    🎨
                    <div class="video-duration">20:30</div>
                </div>
                <div class="video-info">
                    <div class="video-title">【绘画教程】零基础学画画，从入门到精通</div>
                    <div class="video-meta">
                        <div class="video-author">绘画老师</div>
                        <div class="video-stats">
                            <span>👁 234.5万</span>
                            <span>👍 78.9k</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 视频卡片 8 -->
            <div class="video-card">
                <div class="video-thumbnail">
                    🏃‍♂️
                    <div class="video-duration">06:18</div>
                </div>
                <div class="video-info">
                    <div class="video-title">【健身】居家健身指南，每天10分钟轻松瘦身</div>
                    <div class="video-meta">
                        <div class="video-author">健身教练</div>
                        <div class="video-stats">
                            <span>👁 98.4万</span>
                            <span>👍 38.7k</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 视频卡片 9 -->
            <div class="video-card">
                <div class="video-thumbnail">
                    🚗
                    <div class="video-duration">14:55</div>
                </div>
                <div class="video-info">
                    <div class="video-title">【汽车】新能源汽车深度测评，续航能力惊人</div>
                    <div class="video-meta">
                        <div class="video-author">汽车评测</div>
                        <div class="video-stats">
                            <span>👁 76.2万</span>
                            <span>👍 29.5k</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 视频卡片 10 -->
            <div class="video-card">
                <div class="video-thumbnail">
                    📚
                    <div class="video-duration">18:27</div>
                </div>
                <div class="video-info">
                    <div class="video-title">【读书分享】这本书改变了我的人生观</div>
                    <div class="video-meta">
                        <div class="video-author">读书博主</div>
                        <div class="video-stats">
                            <span>👁 123.8万</span>
                            <span>👍 56.3k</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script>
        // 简单的交互功能
        document.addEventListener('DOMContentLoaded', function () {
            // 分类导航点击效果
            const categoryItems = document.querySelectorAll('.category-item');
            categoryItems.forEach(item => {
                item.addEventListener('click', function (e) {
                    e.preventDefault();
                    categoryItems.forEach(cat => cat.classList.remove('active'));
                    this.classList.add('active');
                });
            });

            // 搜索框功能
            const searchInput = document.querySelector('.search-input');
            const searchBtn = document.querySelector('.search-btn');

            searchBtn.addEventListener('click', function () {
                const query = searchInput.value.trim();
                if (query) {
                    alert('搜索: ' + query);
                }
            });

            searchInput.addEventListener('keypress', function (e) {
                if (e.key === 'Enter') {
                    const query = this.value.trim();
                    if (query) {
                        alert('搜索: ' + query);
                    }
                }
            });

            // 视频卡片点击效果
            const videoCards = document.querySelectorAll('.video-card');
            videoCards.forEach(card => {
                card.addEventListener('click', function () {
                    const title = this.querySelector('.video-title').textContent;
                    alert('播放视频: ' + title);
                });
            });
        });
    </script>
</body>

</html>